package top.lacrus.waveycapes;

import net.minecraft.client.Minecraft;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.damagesource.DamageTypes;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = Waveycapes.MODID, value = Dist.CLIENT)
public class AutoJumpHandler {
    
    @SubscribeEvent
    public static void onPlayerHurt(LivingHurtEvent event) {
        // 只处理客户端玩家
        if (!(event.getEntity() instanceof LocalPlayer)) return;
        
        LocalPlayer player = (LocalPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 确保是当前客户端玩家
        if (player != mc.player) return;
        
        DamageSource damageSource = event.getSource();
        Entity directEntity = damageSource.getDirectEntity();
        Entity causingEntity = damageSource.getEntity();
        
        boolean shouldJump = false;
        
        // 检查是否为近战攻击伤害（来自另一名玩家）
        if (causingEntity instanceof Player && causingEntity != player) {
            // 检查是否为近战攻击（直接实体为null或为玩家本身）
            if (directEntity == null || directEntity == causingEntity) {
                shouldJump = true;
            }
        }
        
        // 检查是否为箭矢伤害
        if (directEntity instanceof AbstractArrow) {
            AbstractArrow arrow = (AbstractArrow) directEntity;
            // 确保箭矢是由另一名玩家射出的
            if (arrow.getOwner() instanceof Player && arrow.getOwner() != player) {
                shouldJump = true;
            }
        }
        
        // 如果满足条件，立即跳跃
        if (shouldJump && player.onGround()) {
            // 使用官方API跳跃
            player.jumpFromGround();
        }
    }
}
