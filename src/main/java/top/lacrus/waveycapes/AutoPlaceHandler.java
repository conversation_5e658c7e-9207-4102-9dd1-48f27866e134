package top.lacrus.waveycapes;

import net.minecraft.client.Minecraft;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.InputEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = Waveycapes.MODID, value = Dist.CLIENT)
public class AutoPlaceHandler {
    
    private static boolean autoPlaceActive = false;
    private static long lastPlaceTime = 0;
    private static final long PLACE_INTERVAL = 10; // 10ms间隔
    
    @SubscribeEvent
    public static void onKeyInput(InputEvent.Key event) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return;
        
        if (KeyBindings.AUTO_PLACE_KEY.consumeClick()) {
            autoPlaceActive = !autoPlaceActive;
            if (autoPlaceActive) {
                mc.player.displayClientMessage(
                    net.minecraft.network.chat.Component.literal("§a自动放置方块已开启"), 
                    true
                );
            } else {
                mc.player.displayClientMessage(
                    net.minecraft.network.chat.Component.literal("§c自动放置方块已关闭"), 
                    true
                );
            }
        }
    }
    
    @SubscribeEvent
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;
        
        Minecraft mc = Minecraft.getInstance();
        LocalPlayer player = mc.player;
        Level level = mc.level;
        
        if (player == null || level == null || !autoPlaceActive) return;
        
        // 检查间隔时间
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastPlaceTime < PLACE_INTERVAL) return;
        
        // 检查玩家手中是否有方块物品
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof BlockItem)) return;
        
        // 获取玩家视线方向的方块位置
        HitResult hitResult = mc.hitResult;
        if (hitResult == null || hitResult.getType() != HitResult.Type.BLOCK) return;
        
        BlockHitResult blockHitResult = (BlockHitResult) hitResult;
        BlockPos targetPos = blockHitResult.getBlockPos();
        Direction face = blockHitResult.getDirection();
        
        // 计算要放置方块的位置
        BlockPos placePos = targetPos.relative(face);
        
        // 检查距离是否在4.5格内
        Vec3 playerPos = player.position();
        Vec3 placeBlockCenter = Vec3.atCenterOf(placePos);
        double distance = playerPos.distanceTo(placeBlockCenter);
        
        if (distance > 4.5) return;
        
        // 检查位置是否可以放置方块
        BlockState currentState = level.getBlockState(placePos);
        if (!currentState.canBeReplaced()) return;
        
        // 检查玩家是否与放置位置重叠
        if (placePos.equals(BlockPos.containing(player.position())) || 
            placePos.equals(BlockPos.containing(player.position().add(0, 1, 0)))) {
            return;
        }
        
        // 使用官方API放置方块
        try {
            InteractionResult result = mc.gameMode.useItemOn(
                player, 
                InteractionHand.MAIN_HAND, 
                blockHitResult
            );
            
            if (result.consumesAction()) {
                lastPlaceTime = currentTime;
            }
        } catch (Exception e) {
            // 忽略放置失败的情况
        }
    }
}
